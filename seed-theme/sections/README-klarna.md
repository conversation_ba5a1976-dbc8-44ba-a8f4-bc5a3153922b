# Klarna Payment Info Section

## Описание
Новая секция для отображения информации о платежах Klarna с интерактивным модальным окном.

## Особенности
- ✅ Полностью респонсивный дизайн
- ✅ Глубокая кастомизация через админ панель Shopify
- ✅ Интерактивное модальное окно
- ✅ Поддержка кастомных логотипов
- ✅ Оптимизирован для мобильных устройств
- ✅ Соответствует дизайну из макета

## Как добавить секцию

### 1. В админ панели Shopify:
1. Перейдите в **Online Store > Themes**
2. Нажмите **Customize** на активной теме
3. Найдите место, где хотите добавить секцию (обычно после секции Steps)
4. Нажмите **Add section**
5. Выберите **Klarna Payment Info**

### 2. Настройка секции:

#### Banner Settings:
- **Title**: Основной заголовок (по умолчанию: "Shop now. Pay in 4 interest-free payments")
- **Subtitle**: Подзаголовок (по умолчанию: "As low as $42.25/mo for Upsteps")
- **Klarna Logo**: Загрузите кастомный логотип Klarna (опционально)
- **Learn More Text**: Текст ссылки (по умолчанию: "Learn more")

#### Banner Design:
- **Banner Background**: Цвет фона баннера (#BEEBFC)
- **Banner Max Width**: Максимальная ширина баннера (1098px)
- **Banner Height**: Высота баннера (149px)
- **Border Radius**: Скругление углов (14px)

#### Typography:
- **Title Color**: Цвет заголовка (#002366)
- **Title Font Size**: Размер шрифта заголовка (24px)
- **Title Font Weight**: Жирность шрифта (Black/900)
- **Subtitle Color**: Цвет подзаголовка (#002366)
- **Subtitle Font Size**: Размер шрифта подзаголовка (16px)
- **Brand Background**: Фон для текста "Klarna." (#002366)
- **Brand Text Color**: Цвет текста "Klarna." (белый)
- **Learn More Link Color**: Цвет ссылки "Learn more" (#002366)

#### Modal Settings:
- **Modal Logo**: Логотип для модального окна (опционально)
- **Modal Title**: Заголовок модального окна
- **Modal Subtitle**: Подзаголовок модального окна
- **Step 1-4 Text**: Тексты для шагов в модальном окне
- **Terms Text**: Текст условий использования
- **Close Button Text**: Текст кнопки закрытия

#### Modal Design:
- **Modal Background**: Фон модального окна (белый)
- **Modal Overlay Color**: Цвет оверлея (rgba(0, 0, 0, 0.5))
- **Modal Max Width**: Максимальная ширина модального окна (600px)
- **Modal Border Radius**: Скругление углов модального окна (16px)
- **Modal Title Color**: Цвет заголовка в модальном окне
- **Modal Subtitle Background**: Фон подзаголовка в модальном окне
- **Klarna Highlight Background**: Фон выделения "Klarna." в тексте
- **Close Button Background**: Фон кнопки закрытия
- **Close Button Text Color**: Цвет текста кнопки закрытия

#### Spacing:
- **Section Padding Top**: Отступ сверху секции (40px)
- **Section Padding Bottom**: Отступ снизу секции (40px)

#### Mobile Settings:
- **Mobile Banner Min Height**: Минимальная высота баннера на мобильных (120px)
- **Mobile Title Font Size**: Размер заголовка на мобильных (20px)
- **Mobile Subtitle Font Size**: Размер подзаголовка на мобильных (14px)

## Функциональность

### Модальное окно:
- Открывается по клику на "Learn more"
- Закрывается по клику на крестик, оверлей или клавишу Escape
- Содержит информацию о 4 шагах использования Klarna
- Полностью кастомизируемое содержимое

### Респонсивность:
- **Desktop**: Полный размер согласно макету
- **Tablet**: Адаптивные размеры
- **Mobile**: Оптимизированные размеры и отступы
- **Small Mobile**: Дополнительные оптимизации для маленьких экранов

## Технические детали

### CSS классы:
- `.klarna-section` - основная секция
- `.klarna-banner` - баннер с информацией
- `.klarna-modal` - модальное окно
- `.klarna-modal.active` - активное состояние модального окна

### JavaScript функции:
- `openKlarnaModal()` - открытие модального окна
- `closeKlarnaModal()` - закрытие модального окна

### Shopify Liquid:
- Использует `section.settings` для всех настроек
- Поддерживает `image_picker` для логотипов
- Имеет значения по умолчанию для всех полей

## Размещение
Рекомендуется размещать секцию сразу после секции Steps на главной странице или странице продукта.
