<section class="klarna-section">
  <div class="klarna-container">
    <div class="klarna-banner">
      <div class="klarna-content">
        <h2 class="klarna-title">{{ section.settings.title | default: 'Shop now. Pay in 4 interest-free payments' }}</h2>
        <p class="klarna-subtitle">{{ section.settings.subtitle | default: 'As low as $42.25/mo for Upsteps' }}</p>
        <div class="klarna-brand">
          {%- if section.settings.klarna_logo != blank -%}
            <img src="{{ section.settings.klarna_logo | image_url: width: 100 }}" alt="Klarna" class="klarna-logo" width="100" height="24">
          {%- else -%}
            <span class="klarna-text">Klarna.</span>
          {%- endif -%}
          <button class="klarna-learn-more" onclick="openKlarnaModal()">{{ section.settings.learn_more_text | default: 'Learn more' }}</button>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Klarna <PERSON> -->
<div id="klarna-modal" class="klarna-modal">
  <div class="klarna-modal-overlay" onclick="closeKlarnaModal()"></div>
  <div class="klarna-modal-content">
    <div class="klarna-modal-header">
      {%- if section.settings.modal_logo != blank -%}
        <img src="{{ section.settings.modal_logo | image_url: width: 120 }}" alt="Klarna" class="klarna-modal-logo" width="120" height="32">
      {%- else -%}
        <span class="klarna-modal-brand">Klarna.</span>
      {%- endif -%}
      <button class="klarna-modal-close" onclick="closeKlarnaModal()">×</button>
    </div>
    
    <div class="klarna-modal-body">
      <h3 class="klarna-modal-title">{{ section.settings.modal_title | default: '4 interest-free payments of $62.25' }}</h3>
      <p class="klarna-modal-subtitle">{{ section.settings.modal_subtitle | default: 'Buy what you love and split the cost. It is easy and interest-free.' }}</p>
      
      <div class="klarna-steps">
        <div class="klarna-step">
          <span class="klarna-step-bullet">■</span>
          <span class="klarna-step-text">{{ section.settings.step_1 | default: 'Add item(s) to your cart' }}</span>
        </div>
        <div class="klarna-step">
          <span class="klarna-step-bullet">■</span>
          <span class="klarna-step-text">{{ section.settings.step_2 | default: 'Go to checkout and choose' }} 
            <span class="klarna-highlight">Klarna.</span>
          </span>
        </div>
        <div class="klarna-step">
          <span class="klarna-step-bullet">■</span>
          <span class="klarna-step-text">{{ section.settings.step_3 | default: 'Enter your debit or credit card information' }}</span>
        </div>
        <div class="klarna-step">
          <span class="klarna-step-bullet">■</span>
          <span class="klarna-step-text">{{ section.settings.step_4 | default: 'Your first payment is taken when the order is processed and the remaining 3 are automatically taken every two weeks' }}</span>
        </div>
      </div>
      
      <div class="klarna-terms">
        <p>{{ section.settings.terms_text | default: 'See payment terms. A higher initial payment may be required for some consumers. CA residents: Loans made or arranged pursuant to a California Financing Law license.' }}</p>
      </div>
    </div>
    
    <div class="klarna-modal-footer">
      <button class="klarna-close-button" onclick="closeKlarnaModal()">{{ section.settings.close_button_text | default: 'Close' }}</button>
    </div>
  </div>
</div>

<style>
  .klarna-section {
    padding: {{ section.settings.section_padding_top | default: 40 }}px 20px {{ section.settings.section_padding_bottom | default: 40 }}px;
    background: {{ section.settings.section_background | default: 'transparent' }};
  }

  .klarna-container {
    max-width: {{ section.settings.container_width | default: 1200 }}px;
    margin: 0 auto;
  }

  .klarna-banner {
    width: 100%;
    max-width: {{ section.settings.banner_width | default: 1098 }}px;
    height: {{ section.settings.banner_height | default: 149 }}px;
    margin: 0 auto;
    background: {{ section.settings.banner_background | default: '#BEEBFC' }};
    border-radius: {{ section.settings.banner_border_radius | default: 14 }}px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
  }

  .klarna-content {
    text-align: center;
    width: 100%;
  }

  .klarna-title {
    color: {{ section.settings.title_color | default: '#002366' }};
    font-family: {{ section.settings.title_font | default: 'Lato, sans-serif' }};
    font-size: {{ section.settings.title_font_size | default: 24 }}px;
    font-weight: {{ section.settings.title_font_weight | default: 900 }};
    line-height: {{ section.settings.title_line_height | default: 24 }}px;
    letter-spacing: {{ section.settings.title_letter_spacing | default: 0.64 }}px;
    margin: 0 0 8px 0;
  }

  .klarna-subtitle {
    color: {{ section.settings.subtitle_color | default: '#002366' }};
    font-family: {{ section.settings.subtitle_font | default: 'Lato, sans-serif' }};
    font-size: {{ section.settings.subtitle_font_size | default: 16 }}px;
    font-weight: {{ section.settings.subtitle_font_weight | default: 400 }};
    letter-spacing: {{ section.settings.subtitle_letter_spacing | default: 0.533 }}px;
    margin: 0 0 12px 0;
  }

  .klarna-brand {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
  }

  .klarna-text {
    background: {{ section.settings.brand_background | default: '#002366' }};
    color: {{ section.settings.brand_text_color | default: 'white' }};
    padding: 4px 8px;
    border-radius: 4px;
    font-family: {{ section.settings.brand_font | default: 'Lato, sans-serif' }};
    font-weight: 700;
    font-size: 14px;
  }

  .klarna-logo {
    height: 24px;
    width: auto;
  }

  .klarna-learn-more {
    background: none;
    border: none;
    color: {{ section.settings.link_color | default: '#002366' }};
    font-family: {{ section.settings.link_font | default: 'Lato, sans-serif' }};
    font-size: {{ section.settings.link_font_size | default: 13 }}px;
    font-weight: {{ section.settings.link_font_weight | default: 400 }};
    letter-spacing: {{ section.settings.link_letter_spacing | default: 0.433 }}px;
    text-decoration: underline;
    cursor: pointer;
    padding: 0;
  }

  .klarna-learn-more:hover {
    opacity: 0.8;
  }

  /* Modal Styles */
  .klarna-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
  }

  .klarna-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .klarna-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: {{ section.settings.modal_overlay_color | default: 'rgba(0, 0, 0, 0.5)' }};
  }

  .klarna-modal-content {
    position: relative;
    background: {{ section.settings.modal_background | default: 'white' }};
    border-radius: {{ section.settings.modal_border_radius | default: 16 }}px;
    max-width: {{ section.settings.modal_width | default: 600 }}px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }

  .klarna-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .klarna-modal-brand {
    font-family: {{ section.settings.modal_brand_font | default: 'Lato, sans-serif' }};
    font-size: 20px;
    font-weight: 700;
    color: {{ section.settings.modal_brand_color | default: '#002366' }};
  }

  .klarna-modal-logo {
    height: 32px;
    width: auto;
  }

  .klarna-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: {{ section.settings.modal_close_color | default: '#666' }};
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .klarna-modal-close:hover {
    color: {{ section.settings.modal_close_hover_color | default: '#000' }};
  }

  .klarna-modal-body {
    padding: 24px;
  }

  .klarna-modal-title {
    font-family: {{ section.settings.modal_title_font | default: 'Lato, sans-serif' }};
    font-size: {{ section.settings.modal_title_font_size | default: 24 }}px;
    font-weight: {{ section.settings.modal_title_font_weight | default: 700 }};
    color: {{ section.settings.modal_title_color | default: '#000' }};
    margin: 0 0 12px 0;
    line-height: 1.3;
  }

  .klarna-modal-subtitle {
    font-family: {{ section.settings.modal_subtitle_font | default: 'Lato, sans-serif' }};
    font-size: {{ section.settings.modal_subtitle_font_size | default: 16 }}px;
    color: {{ section.settings.modal_subtitle_color | default: '#666' }};
    margin: 0 0 24px 0;
    padding: 8px 12px;
    background: {{ section.settings.modal_subtitle_background | default: '#e6f3ff' }};
    border-radius: 6px;
  }

  .klarna-steps {
    margin: 24px 0;
  }

  .klarna-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    font-family: {{ section.settings.modal_step_font | default: 'Lato, sans-serif' }};
    font-size: {{ section.settings.modal_step_font_size | default: 14 }}px;
    color: {{ section.settings.modal_step_color | default: '#333' }};
  }

  .klarna-step-bullet {
    color: {{ section.settings.modal_bullet_color | default: '#000' }};
    margin-right: 12px;
    margin-top: 2px;
    flex-shrink: 0;
  }

  .klarna-step-text {
    line-height: 1.4;
  }

  .klarna-highlight {
    background: {{ section.settings.modal_highlight_background | default: '#ffb3d9' }};
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
  }

  .klarna-terms {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }

  .klarna-terms p {
    font-family: {{ section.settings.modal_terms_font | default: 'Lato, sans-serif' }};
    font-size: {{ section.settings.modal_terms_font_size | default: 12 }}px;
    color: {{ section.settings.modal_terms_color | default: '#666' }};
    line-height: 1.4;
    margin: 0;
  }

  .klarna-modal-footer {
    padding: 16px 24px 24px;
    text-align: center;
  }

  .klarna-close-button {
    background: {{ section.settings.modal_button_background | default: '#000' }};
    color: {{ section.settings.modal_button_color | default: 'white' }};
    border: none;
    padding: 12px 32px;
    border-radius: {{ section.settings.modal_button_border_radius | default: 6 }}px;
    font-family: {{ section.settings.modal_button_font | default: 'Lato, sans-serif' }};
    font-size: {{ section.settings.modal_button_font_size | default: 14 }}px;
    font-weight: {{ section.settings.modal_button_font_weight | default: 600 }};
    cursor: pointer;
    transition: opacity 0.2s ease;
  }

  .klarna-close-button:hover {
    opacity: 0.9;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .klarna-section {
      padding: {{ section.settings.mobile_section_padding_top | default: 30 }}px 15px {{ section.settings.mobile_section_padding_bottom | default: 30 }}px;
    }

    .klarna-banner {
      height: auto;
      min-height: {{ section.settings.mobile_banner_height | default: 120 }}px;
      padding: 20px 15px;
    }

    .klarna-title {
      font-size: {{ section.settings.mobile_title_font_size | default: 20 }}px;
      line-height: {{ section.settings.mobile_title_line_height | default: 22 }}px;
      letter-spacing: {{ section.settings.mobile_title_letter_spacing | default: 0.5 }}px;
    }

    .klarna-subtitle {
      font-size: {{ section.settings.mobile_subtitle_font_size | default: 14 }}px;
      letter-spacing: {{ section.settings.mobile_subtitle_letter_spacing | default: 0.4 }}px;
    }

    .klarna-brand {
      flex-direction: column;
      gap: 8px;
    }

    .klarna-learn-more {
      font-size: {{ section.settings.mobile_link_font_size | default: 12 }}px;
    }

    .klarna-modal-content {
      width: 95%;
      margin: 20px;
    }

    .klarna-modal-header {
      padding: 20px 20px 12px;
    }

    .klarna-modal-body {
      padding: 20px;
    }

    .klarna-modal-title {
      font-size: {{ section.settings.mobile_modal_title_font_size | default: 20 }}px;
    }

    .klarna-modal-subtitle {
      font-size: {{ section.settings.mobile_modal_subtitle_font_size | default: 14 }}px;
    }

    .klarna-step {
      font-size: {{ section.settings.mobile_modal_step_font_size | default: 13 }}px;
    }

    .klarna-terms p {
      font-size: {{ section.settings.mobile_modal_terms_font_size | default: 11 }}px;
    }

    .klarna-modal-footer {
      padding: 12px 20px 20px;
    }
  }

  @media (max-width: 480px) {
    .klarna-banner {
      padding: 15px 10px;
    }

    .klarna-title {
      font-size: {{ section.settings.small_mobile_title_font_size | default: 18 }}px;
    }

    .klarna-subtitle {
      font-size: {{ section.settings.small_mobile_subtitle_font_size | default: 13 }}px;
    }
  }
</style>

<script>
  function openKlarnaModal() {
    document.getElementById('klarna-modal').classList.add('active');
    document.body.style.overflow = 'hidden';
  }

  function closeKlarnaModal() {
    document.getElementById('klarna-modal').classList.remove('active');
    document.body.style.overflow = '';
  }

  // Close modal on Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeKlarnaModal();
    }
  });
</script>

{% schema %}
{
  "name": "Klarna Payment Info",
  "tag": "section",
  "class": "shopify-section-klarna",
  "settings": [
    {
      "type": "header",
      "content": "Banner Settings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Shop now. Pay in 4 interest-free payments"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "As low as $42.25/mo for Upsteps"
    },
    {
      "type": "image_picker",
      "id": "klarna_logo",
      "label": "Klarna Logo (optional)"
    },
    {
      "type": "text",
      "id": "learn_more_text",
      "label": "Learn More Text",
      "default": "Learn more"
    },
    {
      "type": "header",
      "content": "Banner Design"
    },
    {
      "type": "color",
      "id": "banner_background",
      "label": "Banner Background",
      "default": "#BEEBFC"
    },
    {
      "type": "range",
      "id": "banner_width",
      "label": "Banner Max Width (px)",
      "min": 600,
      "max": 1400,
      "step": 50,
      "default": 1098
    },
    {
      "type": "range",
      "id": "banner_height",
      "label": "Banner Height (px)",
      "min": 100,
      "max": 250,
      "step": 10,
      "default": 149
    },
    {
      "type": "range",
      "id": "banner_border_radius",
      "label": "Border Radius (px)",
      "min": 0,
      "max": 30,
      "step": 2,
      "default": 14
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title Color",
      "default": "#002366"
    },
    {
      "type": "range",
      "id": "title_font_size",
      "label": "Title Font Size (px)",
      "min": 16,
      "max": 36,
      "step": 2,
      "default": 24
    },
    {
      "type": "select",
      "id": "title_font_weight",
      "label": "Title Font Weight",
      "options": [
        {"value": "400", "label": "Normal"},
        {"value": "600", "label": "Semi Bold"},
        {"value": "700", "label": "Bold"},
        {"value": "900", "label": "Black"}
      ],
      "default": "900"
    },
    {
      "type": "color",
      "id": "subtitle_color",
      "label": "Subtitle Color",
      "default": "#002366"
    },
    {
      "type": "range",
      "id": "subtitle_font_size",
      "label": "Subtitle Font Size (px)",
      "min": 12,
      "max": 24,
      "step": 1,
      "default": 16
    },
    {
      "type": "color",
      "id": "brand_background",
      "label": "Brand Background",
      "default": "#002366"
    },
    {
      "type": "color",
      "id": "brand_text_color",
      "label": "Brand Text Color",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Learn More Link Color",
      "default": "#002366"
    },
    {
      "type": "header",
      "content": "Modal Settings"
    },
    {
      "type": "image_picker",
      "id": "modal_logo",
      "label": "Modal Logo (optional)"
    },
    {
      "type": "text",
      "id": "modal_title",
      "label": "Modal Title",
      "default": "4 interest-free payments of $62.25"
    },
    {
      "type": "textarea",
      "id": "modal_subtitle",
      "label": "Modal Subtitle",
      "default": "Buy what you love and split the cost. It's easy and interest-free."
    },
    {
      "type": "text",
      "id": "step_1",
      "label": "Step 1 Text",
      "default": "Add item(s) to your cart"
    },
    {
      "type": "text",
      "id": "step_2",
      "label": "Step 2 Text",
      "default": "Go to checkout and choose"
    },
    {
      "type": "text",
      "id": "step_3",
      "label": "Step 3 Text",
      "default": "Enter your debit or credit card information"
    },
    {
      "type": "textarea",
      "id": "step_4",
      "label": "Step 4 Text",
      "default": "Your first payment is taken when the order is processed and the remaining 3 are automatically taken every two weeks"
    },
    {
      "type": "textarea",
      "id": "terms_text",
      "label": "Terms Text",
      "default": "See payment terms. A higher initial payment may be required for some consumers. CA residents: Loans made or arranged pursuant to a California Financing Law license."
    },
    {
      "type": "text",
      "id": "close_button_text",
      "label": "Close Button Text",
      "default": "Close"
    },
    {
      "type": "header",
      "content": "Modal Design"
    },
    {
      "type": "color",
      "id": "modal_background",
      "label": "Modal Background",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "id": "modal_overlay_color",
      "label": "Modal Overlay Color",
      "default": "rgba(0, 0, 0, 0.5)"
    },
    {
      "type": "range",
      "id": "modal_width",
      "label": "Modal Max Width (px)",
      "min": 400,
      "max": 800,
      "step": 50,
      "default": 600
    },
    {
      "type": "range",
      "id": "modal_border_radius",
      "label": "Modal Border Radius (px)",
      "min": 0,
      "max": 30,
      "step": 2,
      "default": 16
    },
    {
      "type": "color",
      "id": "modal_title_color",
      "label": "Modal Title Color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "modal_subtitle_background",
      "label": "Modal Subtitle Background",
      "default": "#e6f3ff"
    },
    {
      "type": "color",
      "id": "modal_highlight_background",
      "label": "Klarna Highlight Background",
      "default": "#ffb3d9"
    },
    {
      "type": "color",
      "id": "modal_button_background",
      "label": "Close Button Background",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "modal_button_color",
      "label": "Close Button Text Color",
      "default": "#FFFFFF"
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "section_padding_top",
      "label": "Section Padding Top (px)",
      "min": 0,
      "max": 100,
      "step": 5,
      "default": 40
    },
    {
      "type": "range",
      "id": "section_padding_bottom",
      "label": "Section Padding Bottom (px)",
      "min": 0,
      "max": 100,
      "step": 5,
      "default": 40
    },
    {
      "type": "header",
      "content": "Mobile Settings"
    },
    {
      "type": "range",
      "id": "mobile_banner_height",
      "label": "Mobile Banner Min Height (px)",
      "min": 80,
      "max": 200,
      "step": 10,
      "default": 120
    },
    {
      "type": "range",
      "id": "mobile_title_font_size",
      "label": "Mobile Title Font Size (px)",
      "min": 14,
      "max": 28,
      "step": 2,
      "default": 20
    },
    {
      "type": "range",
      "id": "mobile_subtitle_font_size",
      "label": "Mobile Subtitle Font Size (px)",
      "min": 12,
      "max": 20,
      "step": 1,
      "default": 14
    }
  ],
  "presets": [
    {
      "name": "Klarna Payment Info"
    }
  ]
}
{% endschema %}
